@extends('backend.layouts.master')

@section('main-content')
<div class="container-fluid py-4">
    <div class="card shadow-sm border-0">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Create Order</h5>
        </div>
        <div class="card-body p-4">
            <form method="post" action="{{ route('salesman.orders.store') }}" enctype="multipart/form-data" id="create-order">
                @csrf

                {{-- Customer --}}
                <div class="border rounded p-3 mb-3">
                    <label for="user_id" class="form-label fw-semibold">Select Customer <span class="text-danger">*</span></label>
                    <select name="user_id" id="user_id" class="form-select select2" required>
                        <option value="">-- Select Customer --</option>
                        <option value="add_new">Add New Customer</option>
                    </select>
                    @error('user_id')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                </div>

                {{-- Shipping Address Display --}}
                <div class="border rounded p-3 mb-3">
                    <label class="form-label fw-semibold">Shipping Address</label>
                    <textarea name="address1" id="shipping-address" class="form-control" readonly></textarea>
                    <input type="hidden" name="shipping_price" id="shipping-price" value="0">
                </div>

                {{-- Delivery Method --}}
                <div class="border rounded p-3 mb-3">
                    <label for="delivery_method" class="form-label fw-semibold">Delivery Method <span class="text-danger">*</span></label>
                    <select name="delivery_method" id="delivery_method" class="form-select" required>
                        <option value="pickup">Pickup</option>
                        <option value="ship">Ship</option>
                        <option value="delivery">Delivery</option>
                        <option value="instant">Instant Delivery</option>
                    </select>
                </div>

                {{-- Checkboxes --}}
                <div class="border rounded p-3 mb-3">
                    <div class="form-check mb-2">
                        <input type="checkbox" name="safe_for_future" class="form-check-input" id="safe_for_future">
                        <label for="safe_for_future" class="form-check-label">Save for Future</label>
                    </div>
                    <div class="form-check mb-2" id="is-paid-section" style="display:none;">
                        <input type="checkbox" name="is_paid" class="form-check-input" id="is_paid">
                        <label for="is_paid" class="form-check-label">Mark as Paid</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" name="quick_order" class="form-check-input" id="quick_order">
                        <label for="quick_order" class="form-check-label">Quick Order</label>
                    </div>
                </div>

                <!-- Quick Order Panel (hidden by default) -->
                <div class="row mb-4" id="quick_order_panel" style="display:none;">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6>Select from Previous Orders</h6>
                                <button type="button" class="btn btn-sm btn-primary" id="addSelectedOrders">Add Selected to Order</button>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm" id="previousOrdersTable">
                                    <thead>
                                        <tr>
                                            <th>Select</th>
                                            <th>Order Date</th>
                                            <th>Items</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Signature (hidden by default for non-instant) --}}
                <div class="border rounded p-3 mb-3" id="signature-section" style="display:none;">
                    <label class="form-label fw-semibold">Customer Signature <span class="text-danger">*</span></label>
                    <div class="border rounded bg-light p-2">
                        <canvas id="signature-pad" class="rounded" width="400" height="200"></canvas>
                    </div>
                    <input type="hidden" name="signature_data" id="signature_data">
                    <button type="button" class="btn btn-outline-danger btn-sm mt-2" id="clear-signature">Clear Signature</button>
                </div>

                {{-- Order Items Table --}}
                <div class="border rounded p-3 mb-3">
                    <h5 class="fw-semibold mb-3">Order Items</h5>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Original Price</th>
                                <th>Custom Price</th>
                                <th>Save Custom Price</th>
                                <th>Qty</th>
                                <th>Total</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="items">
                            <tr class="item-row">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <select name="items[0][product_id]" class="form-select select2 product_dropdown" required>
                                            <option value="">-- Select Product --</option>
                                        </select>
                                    </div>
                                    <input name="items[0][color]" type="hidden" class="form-control">
                                </td>
                                <td>
                                    <input name="items[0][original_price]" type="number" step="0.01" class="form-control original-price" readonly>
                                </td>
                                <td>
                                    <input name="items[0][price]" type="number" step="0.01" class="form-control price" readonly>
                                </td>
                                <td>
                                    <input type="checkbox" name="items[0][use_custom_price]" class="form-check-input use-custom-price ml-3">
                                    <input type="hidden" name="items[0][is_one_time]" value="1">
                                </td>
                                <td>
                                    <input name="items[0][quantity]" type="number" min="1" value="1" class="form-control quantity">
                                </td>
                                <td>
                                    <span class="item-total">0.00</span>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-outline-danger remove-item">Remove</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <button type="button" id="add-item" class="btn btn-outline-primary mt-2">+ Add Item</button>

                    {{-- Summary Section --}}
                    <table class="table table-bordered mt-3">
                        <tr>
                            <td>Subtotal</td>
                            <td>$<span id="subtotal">0.00</span></td>
                        </tr>
                        <tr>
                            <td>Shipping</td>
                            <td>$<span id="shipping-cost">0.00</span></td>
                        </tr>
                        <tr>
                            <td><strong>Total</strong></td>
                            <td><strong>$<span id="total-price">0.00</span></strong></td>
                        </tr>
                    </table>
                </div>

                {{-- Submit --}}
                <div class="d-flex gap-3">
                    <button type="reset" class="btn btn-outline-warning">Reset</button>
                    <button type="submit" id="submit-order-btn" class="btn btn-success ml-3">Create Order</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="addCustomerModal" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCustomerModalLabel">Add New Customer</h5>
                <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close">x</button>
            </div>
            <div class="modal-body">
                <form method="post" id="add-customer-form">
                    @csrf
                    <!-- Personal Information Section -->
                    <div class="form-section">
                        <h3>Personal Information</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_name" class="col-form-label">First Name<span class="text-danger">*</span></label>
                                    <input id="first_name" type="text" name="first_name" placeholder="Enter first name" value="{{ old('first_name') }}" class="form-control">
                                    @error('first_name')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_name" class="col-form-label">Last Name<span class="text-danger">*</span></label>
                                    <input id="last_name" type="text" name="last_name" placeholder="Enter last name" value="{{ old('last_name') }}" class="form-control">
                                    @error('last_name')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="col-form-label">Email<span class="text-danger">*</span></label>
                                    <input id="email" type="email" name="email" placeholder="Enter email" value="{{ old('email') }}" class="form-control">
                                    @error('email.*')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password" class="col-form-label">Password<span class="text-danger">*</span></label>
                                    <input id="password" type="password" name="password" placeholder="Enter password" class="form-control">
                                    @error('password')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Company Information Section -->
                    <div class="form-section">
                        <h3>Company Information</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="company_name" class="col-form-label">Company Name</label>
                                    <input id="company_name" type="text" name="company_name" placeholder="Enter company name" value="{{ old('company_name') }}" class="form-control">
                                    @error('company_name')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Billing Address Section -->
                    <div class="form-section">
                        <h3>Billing Address</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="billing_address" class="col-form-label">Street Address<span class="text-danger">*</span></label>
                                    <input id="billing_address" type="text" name="billing_address" placeholder="Enter billing address" value="{{ old('billing_address') }}" class="form-control">
                                    <input type="hidden" id="billing_lat" name="billing_lat" value="{{old('billing_lat')}}">
                                    <input type="hidden" id="billing_long" name="billing_long" value="{{old('billing_long')}}">
                                    @error('billing_address')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="billing_city" class="col-form-label">City<span class="text-danger">*</span></label>
                                    <input id="billing_city" type="text" name="billing_city" placeholder="Enter city" value="{{ old('billing_city') }}" class="form-control">
                                    @error('billing_city')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="billing_state" class="col-form-label">State<span class="text-danger">*</span></label>
                                    <input id="billing_state" type="text" name="billing_state" placeholder="Enter state" value="{{ old('billing_state') }}" class="form-control">
                                    @error('billing_state')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="billing_zip" class="col-form-label">ZIP Code<span class="text-danger">*</span></label>
                                    <input id="billing_zip" type="text" name="billing_zip" placeholder="Enter ZIP code" value="{{ old('billing_zip') }}" class="form-control">
                                    @error('billing_zip')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Address Section -->
                    <div class="form-section">
                        <h3>Shipping Address</h3>
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="same_as_billing" name="same_as_billing"> Check here if shipping address is the same as billing address
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="shipping_address" class="col-form-label">Street Address<span class="text-danger">*</span></label>
                                    <input id="shipping_address" type="text" name="shipping_address" placeholder="Enter shipping address" value="{{ old('shipping_address') }}" class="form-control">
                                    <input type="hidden" id="shipping_lat" name="shipping_lat" value="{{old('shipping_lat')}}">
                                    <input type="hidden" id="shipping_long" name="shipping_long" value="{{old('shipping_long')}}">
                                    @error('shipping_address')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="shipping_city" class="col-form-label">City<span class="text-danger">*</span></label>
                                    <input id="shipping_city" type="text" name="shipping_city" placeholder="Enter city" value="{{ old('shipping_city') }}" class="form-control">
                                    @error('shipping_city')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="shipping_state" class="col-form-label">State<span class="text-danger">*</span></label>
                                    <input id="shipping_state" type="text" name="shipping_state" placeholder="Enter state" value="{{ old('shipping_state') }}" class="form-control">
                                    @error('shipping_state')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="shipping_zip" class="col-form-label">ZIP Code<span class="text-danger">*</span></label>
                                    <input id="shipping_zip" type="text" name="shipping_zip" placeholder="Enter ZIP code" value="{{ old('shipping_zip') }}" class="form-control">
                                    @error('shipping_zip')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information Section -->
                    <div class="form-section">
                        <h3>Contact Information</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contact_phone" class="col-form-label">Contact Phone Number<span class="text-danger">*</span></label>
                                    <input id="contact_phone" type="text" name="contact_phone" placeholder="Enter contact phone number" value="{{ old('contact_phone') }}" class="form-control">
                                    @error('contact_phone')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="account_phone" class="col-form-label">Account Payable Phone Number</label>
                                    <input id="account_phone" type="text" name="account_phone" placeholder="Enter account payable phone number" value="{{ old('account_phone') }}" class="form-control">
                                    @error('account_phone')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Connection Section -->
                    <div class="form-section">
                        <h3>Connections</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="price_level_lists">Price Level Lists</label>
                                    <select name="price_level_lists[]" id="price_level_lists" class="form-control select2" multiple required>
                                        @foreach($price_level_lists as $price_level_list)
                                            <option value="{{ $price_level_list->id }}"
                                                {{ in_array($price_level_list->id, old('price_level_lists', [])) ? 'selected' : '' }}>
                                                {{ $price_level_list->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('price_level_lists')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="cat_id">Category <span class="text-danger">*</span></label>
                                    <select name="cat_id[]" id="cat_id" class="form-control select2" multiple required>
                                        @foreach($categories as $cat_data)
                                            <option value="{{ $cat_data->id }}"
                                                {{ in_array($cat_data->id, old('cat_id', [])) ? 'selected' : '' }}>
                                                {{ $cat_data->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('cat_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status Section -->
                    <div class="form-section">
                        <h3>Status</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="col-form-label">Status<span class="text-danger">*</span></label>
                                    <select name="status" class="form-control">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                    @error('status')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                    <input type="hidden" name="from_order_create" id="from_order_create" value="true">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="submit-customer-btn">Add Customer</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="orderConfirmationModal" tabindex="-1" aria-labelledby="orderConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderConfirmationModalLabel">Confirm Order Details</h5>
                <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close">x</button>
            </div>
            <div class="modal-body">
                <h6>Customer Information</h6>
                <p><strong>Customer:</strong> <span id="modal-customer"></span></p>
                <p><strong>Shipping Address:</strong> <span id="modal-shipping-address"></span></p>
                <p><strong>Delivery Method:</strong> <span id="modal-delivery-method"></span></p>
                <p><strong>Save for Future:</strong> <span id="modal-save-future"></span></p>
                <p><strong>Mark as Paid:</strong> <span id="modal-is-paid"></span></p>
                <p><strong>Quick Order:</strong> <span id="modal-quick-order"></span></p>

                <h6 class="mt-4">Order Items</h6>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Price</th>
                            <th>Quantity</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody id="modal-items">
                    </tbody>
                </table>

                <h6 class="mt-4">Summary</h6>
                <table class="table table-bordered">
                    <tr>
                        <td>Subtotal</td>
                        <td>$<span id="modal-subtotal">0.00</span></td>
                    </tr>
                    <tr>
                        <td>Shipping</td>
                        <td>$<span id="modal-shipping-cost">0.00</span></td>
                    </tr>
                    <tr>
                        <td><strong>Total</strong></td>
                        <td><strong>$<span id="modal-total-price">0.00</span></strong></td>
                    </tr>
                </table>

                <div id="modal-signature-section" style="display:none;">
                    <h6>Customer Signature</h6>
                    <img id="modal-signature" class="img-fluid" style="max-width: 400px;" />
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirm-order-btn">Confirm Order</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('backend/css/select2.min.css') }}">
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<style>
    .select2-container--default {
        width: 100% !important;
    }
    .select2-container--bootstrap-5,
    .select2-container--default .select2-selection--single {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem;
        height: 38px;
        padding: 0.375rem 0.75rem;
        background-color: #fff;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    .select2-container--default .select2-selection--single:focus {
        border-color: #86b7fe !important;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        outline: 0;
    }
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 22px;
        color: #495057;
    }
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 38px;
    }

    /* Custom price input styling */
    .readonly-input {
        background-color: #f8f9fa !important;
        cursor: pointer !important;
    }

    .readonly-input:hover {
        background-color: #e9ecef !important;
        border-color: #86b7fe !important;
    }

    input[name*="[price]"]:not([readonly]) {
        background-color: #fff !important;
        border-color: #28a745 !important;
    }
    .select2-container--default .select2-results__option {
        padding: 6px 12px;
    }
    .select2-container .select2-selection--single .select2-selection__rendered {
        padding-left: 0;
    }
    canvas {
        max-width: 100%;
        height: auto;
    }
    .table th, .table td {
        vertical-align: middle;
    }
    .item-total {
        display: inline-block;
        width: 100%;
        text-align: center;
    }
    .mr-3 {
        margin-right: 1rem;
    }
    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .form-section h3 {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: bold;
        color: #333;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.1.6/dist/signature_pad.umd.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCdSGqiP_9s6qf1RIF70fG6BmFsjzR9QxU&libraries=places"></script>
<script>
    $(document).ready(function(){

        @if(session('data'))
            $data = @json(session('data'));
            console.log('data', $data);
            Swal.fire({
                title: 'Success!',
                text: `Order ${$data['order_number']} successfully created.`,
                icon: 'success',
                confirmButtonText: 'OK',
                showDenyButton: true,
                showCancelButton: true,
                denyButtonText: 'Send to Customer',
                cancelButtonText: 'View Order',
                confirmButtonText: 'Edit Order'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = `{{ url('/salesman/orders') }}/edit/${$data['id']}`;
                }
                else if (result.isDenied) {
                    fetch(`{{ url('/salesman/orders') }}/${$data['id']}/send`, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}',
                            'Content-Type': 'application/json',
                        },
                    })
                    .then(response => {
                        if (!response.ok) throw new Error('Failed to send order');
                        return response.json();
                    })
                    .then(data => {
                        Swal.fire('Success', 'Order sent to customer!', 'success');
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Swal.fire('Error', 'Something went wrong while sending the order.', 'error');
                    });
                }
                else if (result.dismiss === Swal.DismissReason.cancel) {
                    window.location.href = `{{ url('/salesman/orders') }}/show/${$data['id']}`;
                }
            });
        @endif

        let canvas = document.getElementById('signature-pad');
        let signaturePad = new SignaturePad(canvas);

        function resizeCanvas() {
            let ratio = Math.max(window.devicePixelRatio || 1, 1);
            canvas.width = canvas.offsetWidth * ratio;
            canvas.height = canvas.offsetHeight * ratio;
            canvas.getContext('2d').scale(ratio, ratio);
            signaturePad.clear();
        }
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        document.getElementById('clear-signature').addEventListener('click', function () {
            signaturePad.clear();
        });

        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });

        let productOptions = [];
        window.productStock = {};

        function updateProductOptions(userId) {
            $.ajax({
                url: '{{ route("salesman.products.prices") }}',
                type: 'GET',
                data: { user_id: userId },
                success: function (res) {
                    productOptions = [];
                    window.productStock = {};
                    res.forEach(product => {
                        product.item_colors.forEach(itemColor => {
                            window.productStock[product.id] = window.productStock[product.id] || {};
                            window.productStock[product.id][itemColor.color.id] = itemColor.stock;
                            const option = `
                                <option
                                    value="${product.id}"
                                    data-item-number="${itemColor.item_number}"
                                    data-item-name="${product.title}"
                                    data-price="${itemColor.prices.default_price || itemColor.prices.original_price}"
                                    data-original-price="${itemColor.prices.original_price}"
                                    data-custom-price="${itemColor.prices.custom_price || ''}"
                                    data-default-price="${itemColor.prices.default_price || itemColor.prices.original_price}"
                                    data-color="${itemColor.color.id}"
                                    data-photo="${itemColor.photo ? '{{ asset('storage') }}/' + itemColor.photo : ''}"
                                >
                                    ${itemColor.item_number} - ${product.title} (${itemColor.color.name})
                                </option>`;
                            productOptions.push(option);
                        });
                    });
                    updateAllProductDropdowns();
                },
                error: function () {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Failed to load product prices.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }

        function updateAllProductDropdowns() {
            $('#items .item-row').each(function() {
                const selectElement = $(this).find('select[name*="[product_id]"]');
                const currentValue = selectElement.val();

                if (selectElement.hasClass('select2-hidden-accessible')) {
                    selectElement.select2('destroy').off('change');
                }

                selectElement.html('<option value="">-- Select Product --</option>' + productOptions.join(''));

                let validSelection = false;
                if (currentValue) {
                    selectElement.find('option').each(function() {
                        if ($(this).val() === currentValue) {
                            validSelection = true;
                            return false;
                        }
                    });
                }
                selectElement.val(validSelection ? currentValue : '');

                initSelect2(selectElement);

                if (validSelection) {
                    updateHiddenFields(selectElement);
                } else {
                    const row = selectElement.closest('.item-row');
                    row.find('input[name*="[price]"]').val('');
                    row.find('input[name*="[original_price]"]').val('');
                    row.find('input[name*="[color]"]').val('');
                    row.find('.use-custom-price').prop('checked', false);
                    row.find('input[name*="[is_one_time]"]').val('1');
                    row.find('img').attr('src', '');
                    updateItemTotal(row);
                    calculateTotalPrice();
                }
            });
        }

        function updateHiddenFields(selectElement, price = null) {
            const selectedOption = selectElement.find('option:selected');
            const row = selectElement.closest('.item-row');
            const defaultPrice = parseFloat(selectedOption.data('default-price')) || 0;
            const originalPrice = parseFloat(selectedOption.data('original-price')) || 0;
            const customPrice = selectedOption.data('custom-price') ? parseFloat(selectedOption.data('custom-price')) : null;
            const priceInput = row.find('input[name*="[price]"]');
            const useCustomPriceCheckbox = row.find('.use-custom-price');

            // Set basic fields
            row.find('input[name*="[original_price]"]').val(originalPrice.toFixed(2));
            row.find('input[name*="[color]"]').val(selectedOption.data('color') || '');
            row.find('img').attr('src', selectedOption.data('photo') || '');

            // Handle custom price checkbox and readonly state
            if (customPrice !== null && customPrice !== defaultPrice) {
                // There's a saved custom price different from default
                useCustomPriceCheckbox.prop('checked', true);
                priceInput.prop('readonly', false);
                priceInput.removeClass('readonly-input');
                priceInput.val(customPrice.toFixed(2));
                row.find('input[name*="[is_one_time]"]').val('0');
            } else {
                // No custom price or custom price same as default, use default price
                useCustomPriceCheckbox.prop('checked', false);
                priceInput.prop('readonly', true);
                priceInput.addClass('readonly-input');
                priceInput.val((price !== null ? price : defaultPrice).toFixed(2));
                row.find('input[name*="[is_one_time]"]').val('1');
            }

            updateItemTotal(row);
            calculateTotalPrice();
        }

        function initSelect2(element) {
            element.off('change').select2({
                theme: 'bootstrap-5',
                width: '100%',
                templateResult: formatProductOption,
                templateSelection: formatProductSelection
            }).on('change', function() {
                updateHiddenFields($(this));
                validateStock($(this).closest('.item-row'));
                validateAllStock();
            });
        }

        function formatProductOption(option) {
            if (!option.id) return option.text;
            var photo = $(option.element).data('photo');
            var $option = $('<div class="d-flex align-items-center">' +
                (photo ? '<img src="' + photo + '" class="mr-3" style="width:35px; height:35px; object-fit:cover;">' : '') +
                '<div>' + option.text + '</div></div>');
            return $option;
        }

        function formatProductSelection(option) {
            if (!option.id) return option.text;
            var photo = $(option.element).data('photo');
            var $selection = $('<div class="d-flex align-items-center">' +
                (photo ? '<img src="' + photo + '" class="mr-3" style="width:35px; height:35px; object-fit:cover;">' : '') +
                '<div>' + option.text + '</div></div>');
            return $selection;
        }

        $('.product_dropdown').each(function() {
            initSelect2($(this));
        });

        function loadCustomers(salesmanId, selectedCustomerId) {
            $('#user_id').html('<option>Loading...</option>').trigger('change');
            $.ajax({
                url: "{{ route('salesman.customers', ['id' => '__ID__']) }}".replace('__ID__', salesmanId),
                type: 'GET',
                success: function (res) {
                    let options = '<option value="">-- Select Customer --</option><option value="add_new">Add New Customer</option>';
                    res.forEach(customer => {
                        let selected = customer.id == selectedCustomerId ? 'selected' : '';
                        options += `<option value="${customer.id}" ${selected} data-address="${customer.shipping_address} ${customer.city} ${customer.shipping_state} ${customer.shipping_zip}" data-state="${customer.shipping_state}">${customer.first_name} ${customer.last_name} (${customer.email}) - ${customer.shipping_address} ${customer.city} ${customer.shipping_state} ${customer.shipping_zip}</option>`;
                    });
                    $('#user_id').html(options).trigger('change');
                }
            });
        }

        let previousOrdersTable = $('#previousOrdersTable').DataTable({
            pageLength: 5,
            lengthMenu: [5, 10, 25, 50],
            searching: true,
            ordering: true,
            info: true,
            paging: true,
            language: {
                search: "Search orders:",
                emptyTable: "No previous orders found."
            },
            columnDefs: [
                { orderable: false, targets: 0 }
            ]
        });

        function loadPreviousOrders(customerId) {
            $.ajax({
                url: "{{ route('salesman.customer.orders', ['id' => '__ID__']) }}".replace('__ID__', customerId),
                type: 'GET',
                success: function (res) {
                    previousOrdersTable.clear();
                    if (res.length === 0) {
                        previousOrdersTable.draw();
                        return;
                    }
                    res.forEach(order => {
                        let itemsList = '';
                        let itemsData = [];
                        order.items.forEach(item => {
                            itemsList += `<li>${item.quantity} x ${item.product.title} (${item.color})</li>`;
                            itemsData.push({
                                product_id: item.product_id,
                                quantity: item.quantity,
                                price: item.price,
                                color: item.color,
                                total: (item.price * item.quantity).toFixed(2)
                            });
                        });
                        previousOrdersTable.row.add([
                            `<input type="checkbox" name="previous_orders[]" data-items='${JSON.stringify(itemsData)}'>`,
                            new Date(order.created_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }),
                            `<ul class="list-unstyled">${itemsList}</ul>`,
                            `$${parseFloat(order.total_amount).toFixed(2)}`
                        ]);
                    });
                    previousOrdersTable.draw();
                },
                error: function () {
                    previousOrdersTable.clear().draw();
                }
            });
        }

        let salesmanId = {{ auth()->user()->id }};
        if (salesmanId) {
            loadCustomers(salesmanId, null);
        }

        $('#user_id').on('change', function () {
            const selectedOption = $(this).find('option:selected');
            const value = selectedOption.val();
            if (value === 'add_new') {
                $('#addCustomerModal').modal('show');
                $(this).val('').trigger('change.select2');
                $('#shipping-address').val('');
                $('#shipping-price').val('0');
                $('#shipping-cost').text('0.00');
                previousOrdersTable.clear().draw();
                productOptions = [];
                updateAllProductDropdowns();
                calculateTotalPrice();
            } else {
                const address = selectedOption.data('address') || '';
                const state = selectedOption.data('state') || '';
                const customerId = value;
                $('#shipping-address').val(address);
                calculateShipping(address, state);
                calculateTotalPrice();
                if (customerId) {
                    updateProductOptions(customerId);
                    if ($('#quick_order').is(':checked')) {
                        loadPreviousOrders(customerId);
                    }
                } else {
                    previousOrdersTable.clear().draw();
                    productOptions = [];
                    updateAllProductDropdowns();
                }
            }
        });

        function calculateShipping(address, state) {
            let shippingCost = 0;
            const deliveryMethod = $('#delivery_method').val();
            if (deliveryMethod === 'pickup' || deliveryMethod === 'instant') {
                shippingCost = 0;
            } else if (deliveryMethod === 'ship') {
                if (state) {
                    const stateCosts = {
                        'CA': 10.00,
                        'NY': 15.00,
                        'TX': 12.00,
                    };
                    shippingCost = stateCosts[state.toUpperCase()] || 5.00;
                } else if (address) {
                    shippingCost = address.length > 50 ? 8.00 : 5.00;
                }
            }
            $('#shipping-price').val(shippingCost.toFixed(2));
            $('#shipping-cost').text(shippingCost.toFixed(2));
        }

        $('#delivery_method').on('change', function () {
            const deliveryMethod = $(this).val();
            const selectedOption = $('#user_id').find('option:selected');
            const address = selectedOption.data('address') || '';
            const state = selectedOption.data('state') || '';
            $('#signature-section').toggle(deliveryMethod === 'instant');
            $('#is-paid-section').toggle(deliveryMethod === 'instant');
            calculateShipping(address, state);
            calculateTotalPrice();
        });

        let itemIndex = 1;

        $('#add-item').click(function() {
            let row = $('#items .item-row').first().clone();
            row.find('select, input').each(function() {
                const oldName = $(this).attr('name');
                if (oldName) {
                    const newName = oldName.replace(/\[\d+\]/, `[${itemIndex}]`);
                    $(this).attr('name', newName).val(
                        oldName.includes('quantity') ? '1' :
                        oldName.includes('price') || oldName.includes('original_price') ? '' :
                        oldName.includes('is_one_time') ? '1' : ''
                    );
                }
            });
            row.find('.use-custom-price').prop('checked', false);
            row.find('input[name*="[price]"]').prop('readonly', true);
            row.find('.item-total').text('0.00');
            let selectElement = row.find('select[name^="items"]');
            selectElement.html('<option value="">-- Select Product --</option>' + productOptions.join(''));
            selectElement.val('');
            selectElement.removeClass('select2-hidden-accessible').removeAttr('data-select2-id').next('.select2-container').remove();
            row.find('.stock-feedback').remove();
            row.find('img').attr('src', '');
            $('#items').append(row);
            initSelect2(selectElement);
            itemIndex++;
            calculateTotalPrice();
        });

        $(document).on('click', '.remove-item', function () {
            if ($('#items .item-row').length > 1) {
                $(this).closest('.item-row').remove();
                validateAllStock();
                calculateTotalPrice();
            }
        });

        $(document).on('change', '.use-custom-price', function() {
            const row = $(this).closest('.item-row');
            const isChecked = $(this).is(':checked');
            const selectElement = row.find('select[name*="[product_id]"]');
            const selectedOption = selectElement.find('option:selected');
            const defaultPrice = parseFloat(selectedOption.data('default-price')) || 0;
            const customPrice = selectedOption.data('custom-price') ? parseFloat(selectedOption.data('custom-price')) : null;
            const priceInput = row.find('input[name*="[price]"]');

            row.find('input[name*="[is_one_time]"]').val(isChecked ? '0' : '1');

            if (isChecked) {
                priceInput.prop('readonly', false);
                priceInput.removeClass('readonly-input');
                if (customPrice !== null) {
                    priceInput.val(customPrice.toFixed(2));
                }
                setTimeout(() => priceInput.focus(), 100);
            } else {
                priceInput.prop('readonly', true);
                priceInput.addClass('readonly-input');
                priceInput.val(defaultPrice.toFixed(2));
            }

            updateItemTotal(row);
            calculateTotalPrice();
        });

        // Handle price input click when readonly
        $(document).on('click', 'input[name*="[price]"]', function() {
            const row = $(this).closest('.item-row');
            const isReadonly = $(this).prop('readonly');

            if (isReadonly) {
                // If price input is clicked but readonly, check the custom price checkbox
                row.find('.use-custom-price').prop('checked', true).trigger('change');
            }
        });

        function validateStock(row) {
            const selectElement = row.find('select[name*="[product_id]"]');
            const productId = selectElement.val();
            const selectedOption = selectElement.find('option:selected');
            const quantityInput = row.find('input[name*="[quantity]"]');
            const quantity = parseInt(quantityInput.val()) || 0;
            const colorId = row.find('input[name*="[color]"]').val(selectedOption.data('color') || '');
            row.find('.stock-feedback').remove();

            if (!productId || isNaN(quantity) || quantity <= 0) return true;

            const available = parseInt(window.productStock[productId][colorId.val()]) || 0;
            if (quantity > available) {
                const message = `<small class="invalid-feedback d-block stock-feedback">Only ${available} in stock</small>`;
                quantityInput.after(message);
                quantityInput.val(available);
                updateItemTotal(row);
                calculateTotalPrice();
                return false;
            }

            return true;
        }

        function validateAllStock() {
            let valid = true;
            $('#items .item-row').each(function () {
                const rowValid = validateStock($(this));
                if (!rowValid) valid = false;
            });
            $('#submit-order-btn').prop('disabled', !valid);
        }

        function updateItemTotal(row) {
            const price = parseFloat(row.find('input[name*="[price]"]').val()) || 0;
            const quantity = parseInt(row.find('input[name*="[quantity]"]').val()) || 0;
            const total = price * quantity;
            row.find('.item-total').text(total.toFixed(2));
        }

        function calculateTotalPrice() {
            let subtotal = 0;
            $('#items .item-row').each(function() {
                const price = parseFloat($(this).find('input[name*="[price]"]').val()) || 0;
                const quantity = parseInt($(this).find('input[name*="[quantity]"]').val()) || 0;
                subtotal += price * quantity;
            });
            const shippingCost = parseFloat($('#shipping-price').val()) || 0;
            const total = subtotal + shippingCost;
            $('#subtotal').text(subtotal.toFixed(2));
            $('#total-price').text(total.toFixed(2));
        }

        $(document).on('blur', 'input[name*="[quantity]"]', function () {
            const row = $(this).closest('.item-row');
            // let quantity = parseInt($(this).val()) || 0;
            // if (quantity === 0) {
            //     $(this).val(1);
            //     quantity = 1;
            // }
            // const roundedQuantity = Math.round(quantity / 12) * 12;
            // if (quantity !== roundedQuantity) {
            //     Swal.fire({
            //         title: 'Round to nearest multiple of 12?',
            //         text: `The entered quantity (${quantity}) will be rounded to ${roundedQuantity}. Proceed?`,
            //         icon: 'question',
            //         showCancelButton: true,
            //         confirmButtonColor: '#3085d6',
            //         cancelButtonColor: '#d33',
            //         confirmButtonText: 'Yes, round it!'
            //     }).then((result) => {
            //         if (result.isConfirmed) {
            //             $(this).val(roundedQuantity);
            //         }
            //         validateStock(row);
            //         validateAllStock();
            //         updateItemTotal(row);
            //         calculateTotalPrice();
            //     });
            // } else {
                validateStock(row);
                validateAllStock();
                updateItemTotal(row);
                calculateTotalPrice();
            // }
        });

        $(document).on('change keyup', 'select[name*="[product_id]"], input[name*="[price]"]', function () {
            const row = $(this).closest('.item-row');
            if ($(this).is('select')) {
                updateHiddenFields($(this));
            }
            validateStock(row);
            validateAllStock();
            updateItemTotal(row);
            calculateTotalPrice();
        });

        function populateConfirmationModal() {
            const customer = $('#user_id option:selected').text() || 'Not selected';
            const shippingAddress = $('#shipping-address').val() || 'Not provided';
            const deliveryMethod = $('#delivery_method option:selected').text() || 'Not selected';
            const saveFuture = $('#safe_for_future').is(':checked') ? 'Yes' : 'No';
            const isPaid = $('#is_paid').is(':checked') ? 'Yes' : 'No';
            const quickOrder = $('#quick_order').is(':checked') ? 'Yes' : 'No';
            const subtotal = $('#subtotal').text();
            const shippingCost = $('#shipping-cost').text();
            const totalPrice = $('#total-price').text();
            const signatureData = $('#signature_data').val();

            $('#modal-customer').text(customer);
            $('#modal-shipping-address').text(shippingAddress);
            $('#modal-delivery-method').text(deliveryMethod);
            $('#modal-save-future').text(saveFuture);
            $('#modal-is-paid').text(isPaid);
            $('#modal-quick-order').text(quickOrder);
            $('#modal-subtotal').text(subtotal);
            $('#modal-shipping-cost').text(shippingCost);
            $('#modal-total-price').text(totalPrice);

            $('#modal-items').empty();
            $('#items .item-row').each(function() {
                const product = $(this).find('select[name*="[product_id]"] option:selected').text() || 'Not selected';
                const price = $(this).find('input[name*="[price]"]').val() || '0.00';
                const quantity = $(this).find('input[name*="[quantity]"]').val() || '0';
                const total = $(this).find('.item-total').text() || '0.00';
                $('#modal-items').append(`
                    <tr>
                        <td>${product}</td>
                        <td>$${price}</td>
                        <td>${quantity}</td>
                        <td>$${total}</td>
                    </tr>
                `);
            });

            if (signatureData && $('#delivery_method').val() === 'instant') {
                $('#modal-signature').attr('src', signatureData);
                $('#modal-signature-section').show();
            } else {
                $('#modal-signature-section').hide();
            }
        }

        $('#create-order').on('submit', function (e) {
            e.preventDefault();
            if ($('#delivery_method').val() === 'instant' && signaturePad.isEmpty()) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Customer signature is required for instant delivery.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            const items = [];
            $('#items .item-row').each(function (index) {
                const productId = $(this).find('select[name*="[product_id]"]').val();
                const color = $(this).find('input[name*="[color]"]').val();
                const quantity = parseInt($(this).find('input[name*="[quantity]"]').val()) || 0;
                if (productId && color && quantity) {
                    items.push({
                        product_id: productId,
                        color: color,
                        quantity: quantity
                    });
                }
            });

            $.ajax({
                url: '{{ route("salesman.orders.check-stock") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    items: items
                },
                success: function (response) {
                    if (!signaturePad.isEmpty()) {
                        const dataUrl = signaturePad.toDataURL('image/png');
                        $('#signature_data').val(dataUrl);
                    }
                    populateConfirmationModal();
                    $('#orderConfirmationModal').modal('show');
                },
                error: function (xhr) {
                    if (xhr.status === 422 && xhr.responseJSON.errors) {
                        let errorMessage = 'The following items exceed available stock:\n';
                        xhr.responseJSON.errors.forEach(error => {
                            errorMessage += `- ${error.product}: Requested ${error.requested}, Available ${error.available}\n`;
                        });
                        Swal.fire({
                            title: 'Stock Error!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to validate stock availability.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                }
            });
        });

        $('#confirm-order-btn').on('click', function() {
            $('#orderConfirmationModal').modal('hide');
            $('#create-order').off('submit').submit();
        });

        $('#quick_order').change(function(){
            if($(this).is(':checked')) {
                $('#quick_order_panel').show();
                const customerId = $('#user_id').val();
                if (customerId) {
                    loadPreviousOrders(customerId);
                }
            } else {
                $('#quick_order_panel').hide();
                previousOrdersTable.clear().draw();
            }
        });

        $('#addSelectedOrders').click(function(){
            $('#items .item-row').each(function() {
                const productId = $(this).find('select[name*="[product_id]"]').val();
                if (!productId) {
                    $(this).remove();
                }
            });

            if ($('#items .item-row').length === 0) {
                let row = $('#items .item-row').first().clone();
                row.find('select, input').each(function() {
                    const oldName = $(this).attr('name');
                    if (oldName) {
                        const newName = oldName.replace(/\[\d+\]/, '[0]');
                        $(this).attr('name', newName).val(
                            oldName.includes('quantity') ? '1' :
                            oldName.includes('price') || oldName.includes('original_price') ? '' :
                            oldName.includes('is_one_time') ? '1' : ''
                        );
                    }
                });
                row.find('.use-custom-price').prop('checked', false);
                row.find('.item-total').text('0.00');
                let selectElement = row.find('select[name^="items"]');
                selectElement.html('<option value="">-- Select Product --</option>' + productOptions.join(''));
                selectElement.val('');
                selectElement.removeClass('select2-hidden-accessible').removeAttr('data-select2-id').next('.select2-container').remove();
                row.find('.stock-feedback').remove();
                row.find('img').attr('src', '');
                $('#items').append(row);
                initSelect2(selectElement);
                itemIndex = 1;
            }

            var selectedOrders = [];
            $('#previousOrdersTable input[type="checkbox"]:checked').each(function(){
                var items = $(this).data('items');
                selectedOrders.push({
                    items: items
                });
            });

            if(selectedOrders.length === 0) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Please select at least one previous order',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            selectedOrders.forEach(function(order) {
                order.items.forEach(function(item) {
                    let row = $('#items .item-row').first().clone();
                    row.find('select, input').each(function() {
                        const oldName = $(this).attr('name');
                        if (oldName) {
                            const newName = oldName.replace(/\[\d+\]/, `[${itemIndex}]`);
                            $(this).attr('name', newName);
                            if (oldName.includes('quantity')) {
                                $(this).val(item.quantity);
                            } else if (oldName.includes('price')) {
                                $(this).val(parseFloat(item.price).toFixed(2));
                            } else if (oldName.includes('original_price')) {
                                $(this).val('');
                            } else if (oldName.includes('color')) {
                                $(this).val(item.color);
                            } else if (oldName.includes('is_one_time')) {
                                $(this).val('1');
                            }
                        }
                    });
                    row.find('.use-custom-price').prop('checked', false);
                    row.find('.item-total').text(item.total);
                    let selectElement = row.find('select[name^="items"]');
                    selectElement.html('<option value="">-- Select Product --</option>' + productOptions.join(''));
                    selectElement.removeClass('select2-hidden-accessible').removeAttr('data-select2-id').next('.select2-container').remove();
                    row.find('.stock-feedback').remove();
                    row.find('img').attr('src', '');

                    $('#items').append(row);
                    initSelect2(selectElement);
                    selectElement.val(item.product_id).trigger('change.select2');
                    updateHiddenFields(selectElement, item.price);
                    validateStock(row);
                    validateAllStock();
                    itemIndex++;
                });
                calculateTotalPrice();
            });
        });

        document.getElementById('same_as_billing').addEventListener('change', function() {
            const shippingFields = ['shipping_address', 'shipping_city', 'shipping_state', 'shipping_zip', 'shipping_long', 'shipping_lat'];
            const billingFields = ['billing_address', 'billing_city', 'billing_state', 'billing_zip', 'billing_long', 'billing_lat'];

            if (this.checked) {
                shippingFields.forEach((field, index) => {
                    document.querySelector(`#add-customer-form input[name="${field}"]`).value = document.querySelector(`#add-customer-form input[name="${billingFields[index]}"]`).value;
                });
            } else {
                shippingFields.forEach(field => {
                    document.querySelector(`#add-customer-form input[name="${field}"]`).value = '';
                });
            }
        });

        function initCustomerAutocomplete() {
            if (typeof google === 'undefined' || !google.maps || !google.maps.places) {
                console.error('Google Maps API not loaded');
                return;
            }

            const billingInput = document.getElementById('billing_address');
            const shippingInput = document.getElementById('shipping_address');

            if (!billingInput || !shippingInput) {
                console.error('Address input fields not found');
                return;
            }

            const autocompleteOptions = {
                types: ['address'],
                componentRestrictions: { country: 'us' }
            };

            const billingAutocomplete = new google.maps.places.Autocomplete(billingInput, autocompleteOptions);
            billingAutocomplete.setFields(['address_components', 'geometry']);

            const shippingAutocomplete = new google.maps.places.Autocomplete(shippingInput, autocompleteOptions);
            shippingAutocomplete.setFields(['address_components', 'geometry']);

            billingAutocomplete.addListener('place_changed', function () {
                const place = billingAutocomplete.getPlace();
                if (!place.geometry) return;

                const lat = place.geometry.location.lat();
                const lng = place.geometry.location.lng();
                document.getElementById('billing_lat').value = lat;
                document.getElementById('billing_long').value = lng;

                place.address_components.forEach(component => {
                    const types = component.types;
                    if (types.includes('locality')) {
                        document.getElementById('billing_city').value = component.long_name;
                    }
                    if (types.includes('administrative_area_level_1')) {
                        document.getElementById('billing_state').value = component.long_name;
                    }
                    if (types.includes('postal_code')) {
                        document.getElementById('billing_zip').value = component.long_name;
                    }
                });
            });

            shippingAutocomplete.addListener('place_changed', function () {
                const place = shippingAutocomplete.getPlace();
                if (!place.geometry) return;

                const lat = place.geometry.location.lat();
                const lng = place.geometry.location.lng();
                document.getElementById('shipping_lat').value = lat;
                document.getElementById('shipping_long').value = lng;

                place.address_components.forEach(component => {
                    const types = component.types;
                    if (types.includes('locality')) {
                        document.getElementById('shipping_city').value = component.long_name;
                    }
                    if (types.includes('administrative_area_level_1')) {
                        document.getElementById('shipping_state').value = component.long_name;
                    }
                    if (types.includes('postal_code')) {
                        document.getElementById('shipping_zip').value = component.long_name;
                    }
                });
            });

            [billingInput, shippingInput].forEach(input => {
                input.addEventListener('focus', () => {
                    const autocompleteDropdown = document.querySelector('.pac-container');
                    if (autocompleteDropdown) {
                        autocompleteDropdown.style.zIndex = '2000';
                        autocompleteDropdown.style.position = 'absolute';
                    }
                });
            });
        }

        $('#submit-customer-btn').on('click', function() {
            $.ajax({
                url: "{{ route('salesman.users.store') }}",
                type: 'POST',
                data: $('#add-customer-form').serialize(),
                success: function(response) {
                    $('#addCustomerModal').modal('hide');
                    Swal.fire({
                        title: 'Success!',
                        text: 'Customer added successfully.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        loadCustomers(salesmanId, response.user_id);
                        $('#user_id').val(response.customer.id).trigger('change.select2');
                        $('#add-customer-form')[0].reset();
                    });
                },
                error: function(xhr) {
                    let errors = xhr.responseJSON.errors || {};
                    let errorMessage = 'Please fix the following errors:\n';
                    for (let field in errors) {
                        errorMessage += `- ${errors[field][0]}\n`;
                    }
                    Swal.fire({
                        title: 'Error!',
                        text: errorMessage,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        });

        $('#addCustomerModal').on('shown.bs.modal', function () {
            $('#add-customer-form select').each(function() {
                if (!$(this).hasClass('select2-hidden-accessible')) {
                    $(this).select2({
                        theme: 'bootstrap-5',
                        width: '100%'
                    });
                }
            });

            $('#billing_address, #shipping_address').each(function() {
                if ($(this).hasClass('select2-hidden-accessible')) {
                    $(this).select2('destroy');
                }
            });

            try {
                initCustomerAutocomplete();
            } catch (error) {
                console.error('Failed to initialize Google Maps Autocomplete:', error);
            }
        });

        $('#addCustomerModal').on('hidden.bs.modal', function () {
            $('#add-customer-form')[0].reset();
            $('#add-customer-form select').each(function() {
                if ($(this).hasClass('select2-hidden-accessible')) {
                    $(this).select2('destroy');
                }
            });
            $('#add-customer-form .text-danger').remove();
        });

        calculateTotalPrice();
    });
</script>
@endpush

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserPriceLevelList extends Model
{
    use HasFactory;

    protected $table = 'user_price_level_lists';

    protected $fillable = ['user_id','price_level_list_id'];

    public function itemPriceLevelList()
    {
        return $this->hasOne(ItemPriceLevelList::class, 'price_level_list_id', 'price_level_list_id');
    }

    public function priceLevelList()
    {
        return $this->belongsTo(PriceLevelList::class, 'price_level_list_id', 'id');
    }
}

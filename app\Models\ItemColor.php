<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ItemColor extends Model
{
    use HasFactory;

    protected $table = 'item_colors';

    protected $fillable = ['product_id','color_id', 'product_number', 'barcode', 'price', 'photo', 'item_number', 'stock', 'barcode_number'];

    protected $appends = ['additional_images'];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function color()
    {
        return $this->belongsTo(Color::class, 'color_id');
    }

    public function getAdditionalImagesAttribute()
    {
        return AdditionalItemImages::where('item_color_id', $this->color_id)->where('product_id', $this->product_id)->get();
    }

    public function orderPrices()
    {
        return $this->hasMany(OrderPrice::class);
    }

    public function userPrices()
    {
        return $this->hasMany(UserItemColorPrice::class);
    }

    // public function getPriceForUserAttribute($userId)
    // {
    //     $userPrice = $this->userPrices()->where('user_id', $userId)->where('item_color_id', $this->id)->first();
    //     return [
    //         'original_price' => $this->price,
    //         'custom_price' => $userPrice ? $userPrice->custom_price : $this->price,
    //     ];
    // }

    public function getPriceForUserAttribute($userId)
    {
        $userPrice = $this->userPrices()->where('user_id', $userId)->where('item_color_id', $this->id)->first();
        $priceLevelPrice = UserPriceLevelList::where('user_id', $userId)
                            ->whereHas('priceLevelList', function ($query) {
                                $query->where('status', 'active');
                            })
                            ->with(['itemPriceLevelList' => function ($query) {
                                $query->where('product_id', $this->product_id)
                                    ->where('color_id', $this->color_id)
                                    ->orderBy('custom_price', 'asc');
                            }])
                            ->first();

        if ($priceLevelPrice) {
            dd($priceLevelPrice->itemPriceLevelList);
            $defaultPrice = $priceLevelPrice->itemPriceLevelList->custom_price;

            return [
                'original_price' => $defaultPrice,
                'custom_price' => $userPrice ? $userPrice->custom_price : $defaultPrice,
            ];
        }

        if ($userPrice) {
            return [
                'original_price' => $this->price,
                'custom_price' => $userPrice->custom_price,
            ];
        }

        return [
            'original_price' => $this->price,
            'custom_price' => $this->price,
        ];
    }
}
